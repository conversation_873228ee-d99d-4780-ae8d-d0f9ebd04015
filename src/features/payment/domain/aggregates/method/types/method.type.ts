import { Nullable } from "@heronjs/common";
import { GatewayCodesEnum } from "@features/payment/domain/aggregates/gateway";
import { MethodStatusEnum } from "@features/payment/domain/aggregates/method/enums";

export type CreateMethodInput = {
  code: string;
  gateway: GatewayCodesEnum;
  label: string;
  desc?: Nullable<string>;
  status: MethodStatusEnum;
  visibility: boolean;
  sortOrder: number;
  platformExclusion?: Nullable<any>;
  sourceTypeExclusion?: Nullable<any>;
};

export type UpdateMethodInput = {
  label?: string;
  desc?: Nullable<string>;
  status?: MethodStatusEnum;
  visibility?: boolean;
  sortOrder?: number;
  platformExclusion?: Nullable<any>;
  sourceTypeExclusion?: Nullable<any>;
};
