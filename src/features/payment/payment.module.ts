import { Module } from "@heronjs/common";
import {
  C<PERSON><PERSON>ouponUseCase,
  CreateGatewayUseCase,
  CreateMethodUseCase,
  WebhookHandlerFactory,
  GetCouponByCodeUseCase,
  AddGatewayCouponUseCase,
  TransactionFailedHandler,
  CreateWebhookEventUseCase,
  CreatePaymentMethodUseCase,
  TransactionInitiatedHandler,
  TransactionCompletedHandler,
  GetWebhookEventByEventIdUseCase,
} from "@features/payment/app";
import {
  MethodMapper,
  CouponMapper,
  CouponBuilder,
  GatewayMapper,
  MethodBuilder,
  GatewayBuilder,
  TransactionMapper,
  TransactionBuilder,
  WebhookEventMapper,
  GatewayConfigMapper,
  PaymentMethodMapper,
  WebhookEventBuilder,
  GatewayConfigBuilder,
  PaymentMethodBuilder,
  GatewayCustomerMapper,
  GatewayCustomerBuilder,
} from "@features/payment/domain";
import {
  <PERSON>upon<PERSON><PERSON>,
  MethodDao,
  GatewayDao,
  DatabaseUtil,
  TransactionDao,
  WebhookEventDao,
  GatewayConfigDao,
  PaymentMethodDao,
  CouponRepository,
  MethodRepository,
  GatewayRepository,
  GatewayCustomerDao,
  TransactionRepository,
  WebhookEventRepository,
  GatewayConfigRepository,
  PaymentMethodRepository,
  GatewayCustomerRepository,
} from "@features/payment/infra";
import { AdminCouponRest } from "@features/payment/presentation";

@Module({
  controllers: [AdminCouponRest],
  providers: [
    // Mappers
    CouponMapper,
    GatewayMapper,
    MethodMapper,
    TransactionMapper,
    WebhookEventMapper,
    GatewayConfigMapper,
    PaymentMethodMapper,
    GatewayCustomerMapper,

    // Builders
    CouponBuilder,
    GatewayBuilder,
    GatewayConfigBuilder,
    GatewayCustomerBuilder,
    MethodBuilder,
    PaymentMethodBuilder,
    TransactionBuilder,
    WebhookEventBuilder,

    // DAOs
    CouponDao,
    GatewayDao,
    GatewayConfigDao,
    GatewayCustomerDao,
    MethodDao,
    PaymentMethodDao,
    TransactionDao,
    WebhookEventDao,

    // Repositories
    CouponRepository,
    GatewayRepository,
    GatewayConfigRepository,
    GatewayCustomerRepository,
    MethodRepository,
    PaymentMethodRepository,
    TransactionRepository,
    WebhookEventRepository,

    // Coupon Use Cases
    CreateCouponUseCase,
    GetCouponByCodeUseCase,
    AddGatewayCouponUseCase,

    // Gateway Use Cases
    CreateGatewayUseCase,

    // Method Use Cases
    CreateMethodUseCase,

    // Payment method Use Cases
    CreatePaymentMethodUseCase,

    // Webhook Event Use Cases
    CreateWebhookEventUseCase,
    GetWebhookEventByEventIdUseCase,

    // Utils
    DatabaseUtil,

    // Factories
    WebhookHandlerFactory,

    // Handlers
    TransactionCompletedHandler,
    TransactionFailedHandler,
    TransactionInitiatedHandler,
  ],
})
// @Transporters([KafkaAdapter])
export class PaymentModule {}
