import z from "zod";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  ICouponBuilder,
  CouponStatusEnum,
  CreateCouponInput,
  ICouponRepository,
  CouponDurationEnum,
  CouponAlreadyExistsError,
  CouponInvalidDiscountError,
} from "@features/payment/domain";

export type CreateCouponUseCaseInput = CreateCouponInput;
export type CreateCouponUseCaseOutput = { id: string };

const CreateCouponUseCaseInputSchema = z
  .object({
    id: z.string().min(1, "Coupon ID is required"),
    name: z.string().min(1, "Coupon name is required"),
    code: z.string().min(1, "Coupon code is required").toUpperCase(),
    status: z.nativeEnum(CouponStatusEnum).optional(),
    duration: z.nativeEnum(CouponDurationEnum).optional(),
    percentOff: z.number().min(0).max(100).optional(),
    amountOff: z.number().min(0).optional(),
    effectTo: z.number().min(0).optional(),
    maxRedemptions: z.number().min(1).optional(),
    currency: z.string().length(3).optional(),
  })
  .refine(
    (data) => {
      // Either percentOff or amountOff must be provided, but not both
      const hasPercentOff = data.percentOff !== undefined && data.percentOff !== null;
      const hasAmountOff = data.amountOff !== undefined && data.amountOff !== null;
      return hasPercentOff !== hasAmountOff; // XOR - exactly one should be true
    },
    {
      message: "Either percentOff or amountOff must be provided, but not both",
      path: ["percentOff", "amountOff"],
    },
  )
  .refine(
    (data) => {
      // If amountOff is provided, currency is required
      if (data.amountOff !== undefined && data.amountOff !== null) {
        return data.currency !== undefined && data.currency !== null;
      }
      return true;
    },
    {
      message: "Currency is required when amountOff is provided",
      path: ["currency"],
    },
  );

export type ICreateCouponUseCase = IUseCase<
  CreateCouponUseCaseInput,
  CreateCouponUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.CREATE_COUPON,
  scope: Lifecycle.Transient,
})
export class CreateCouponUseCase
  extends UseCase<CreateCouponUseCaseInput, CreateCouponUseCaseOutput, UseCaseContext>
  implements ICreateCouponUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.COUPON)
    protected readonly builder: ICouponBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.COUPON)
    protected readonly repo: ICouponRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreateCouponUseCaseInput) => {
    const validatedInput = CreateCouponUseCaseInputSchema.parse(input);

    // Check if coupon with the same code already exists
    const existingCoupon = await this.repo.findOne({
      filter: { code: { $eq: validatedInput.code } },
    });
    if (existingCoupon) throw new CouponAlreadyExistsError();

    // Additional business validation
    this.validateDiscountValues(validatedInput);

    // Create new coupon
    const coupon = await this.builder.build({ id: validatedInput.id });
    await coupon.createCoupon({
      id: validatedInput.id,
      name: validatedInput.name,
      code: validatedInput.code,
      status: validatedInput.status || CouponStatusEnum.ACTIVE,
      duration: validatedInput.duration || CouponDurationEnum.ONCE,
      percentOff: validatedInput.percentOff || null,
      amountOff: validatedInput.amountOff || null,
      effectTo: validatedInput.effectTo || null,
      maxRedemptions: validatedInput.maxRedemptions || null,
      currency: validatedInput.currency || null,
    });

    await this.repo.create(coupon);

    return { id: coupon.id };
  };

  private validateDiscountValues(input: CreateCouponInput): void {
    const hasPercentOff = input.percentOff !== undefined && input.percentOff !== null;
    const hasAmountOff = input.amountOff !== undefined && input.amountOff !== null;

    // This should already be caught by Zod, but adding as extra safety
    if (hasPercentOff && hasAmountOff) {
      throw new CouponInvalidDiscountError();
    }

    if (!hasPercentOff && !hasAmountOff) {
      throw new CouponInvalidDiscountError();
    }

    // Validate percentage range
    if (hasPercentOff && (input.percentOff! < 0 || input.percentOff! > 100)) {
      throw new CouponInvalidDiscountError();
    }

    // Validate amount is positive
    if (hasAmountOff && input.amountOff! <= 0) {
      throw new CouponInvalidDiscountError();
    }
  }
}
