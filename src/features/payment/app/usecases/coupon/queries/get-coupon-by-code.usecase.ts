import z from "zod";
import { RuntimeError } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { ICouponRepository, ICouponMapper, CouponDto } from "@features/payment/domain";

export type GetCouponByCodeUseCaseInput = {
  code: string;
  activeOnly?: boolean;
};

export type GetCouponByCodeUseCaseOutput = CouponDto;

const GetCouponByCodeUseCaseInputSchema = z.object({
  code: z.string().min(1, "Coupon code is required").toUpperCase(),
  activeOnly: z.boolean().optional().default(false),
});

export type IGetCouponByCodeUseCase = IUseCase<
  GetCouponByCodeUseCaseInput,
  GetCouponByCodeUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.GET_COUPON_BY_CODE,
  scope: Lifecycle.Transient,
})
export class GetCouponByCodeUseCase
  extends UseCase<GetCouponByCodeUseCaseInput, GetCouponByCodeUseCaseOutput, UseCaseContext>
  implements IGetCouponByCodeUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.COUPON)
    protected readonly repo: ICouponRepository,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.COUPON)
    protected readonly mapper: ICouponMapper,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetCouponByCodeUseCaseInput) => {
    const { code, activeOnly } = GetCouponByCodeUseCaseInputSchema.parse(input);

    // Find the coupon by code
    const filter = activeOnly
      ? { code: { $eq: code }, status: { $eq: "active" } }
      : { code: { $eq: code } };

    const coupon = await this.repo.findOne({
      filter,
    });

    if (!coupon) {
      throw new RuntimeError("coupon", 30000, "Coupon not found");
    }

    // Convert to DTO for response
    return await this.mapper.fromEntityToDto(coupon);
  };
}
