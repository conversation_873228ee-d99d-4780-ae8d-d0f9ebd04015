import z from "zod";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { CreateMethodInput } from "@features/payment/domain/aggregates/method/types";
import {
  IMethodBuilder,
  GatewayCodesEnum,
  MethodStatusEnum,
  IMethodRepository,
  MethodAlreadyExistsError,
} from "@features/payment/domain";

export type CreateMethodUseCaseInput = CreateMethodInput;
export type CreateMethodUseCaseOutput = { code: string };

const CreateMethodUseCaseInputSchema = z.object({
  code: z.string().min(1, "Method code is required"),
  gateway: z.nativeEnum(GatewayCodesEnum),
  label: z.string().min(1, "Method label is required"),
  desc: z.string().nullable().optional(),
  status: z.nativeEnum(MethodStatusEnum),
  visibility: z.boolean(),
  sortOrder: z.number().int().min(0, "Sort order must be a non-negative integer"),
  platformExclusion: z.any().nullable().optional(),
  sourceTypeExclusion: z.any().nullable().optional(),
});

export type ICreateMethodUseCase = IUseCase<
  CreateMethodUseCaseInput,
  CreateMethodUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.CREATE_METHOD,
  scope: Lifecycle.Transient,
})
export class CreateMethodUseCase
  extends UseCase<CreateMethodUseCaseInput, CreateMethodUseCaseOutput, UseCaseContext>
  implements ICreateMethodUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.METHOD)
    protected readonly builder: IMethodBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.METHOD)
    protected readonly repo: IMethodRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreateMethodUseCaseInput) => {
    const validatedInput = CreateMethodUseCaseInputSchema.parse(input);

    // Check if method with the same code already exists
    const existingMethod = await this.repo.findOne({
      filter: { code: { $eq: validatedInput.code } },
    });
    if (existingMethod) throw new MethodAlreadyExistsError();

    // Create new method
    const method = await this.builder.build({ id: validatedInput.code });
    await method.createMethod({
      code: validatedInput.code,
      gateway: validatedInput.gateway,
      label: validatedInput.label,
      desc: validatedInput.desc,
      status: validatedInput.status,
      visibility: validatedInput.visibility,
      sortOrder: validatedInput.sortOrder,
      platformExclusion: validatedInput.platformExclusion,
      sourceTypeExclusion: validatedInput.sourceTypeExclusion,
    });
    await this.repo.create(method);

    return { code: method.code };
  };
}
