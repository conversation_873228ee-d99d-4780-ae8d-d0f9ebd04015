import z from "zod";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  GatewayCodesEnum,
  IPaymentMethodBuilder,
  PaymentMethodTypeEnum,
  CreatePaymentMethodInput,
  IPaymentMethodRepository,
  PaymentMethodFundingEnum,
  PaymentMethodAlreadyExistsError,
  PaymentMethodMultipleDefaultsError,
  PaymentMethodInvalidExpirationError,
} from "@features/payment/domain";

export type CreatePaymentMethodUseCaseInput = CreatePaymentMethodInput;
export type CreatePaymentMethodUseCaseOutput = { id: string };

const CreatePaymentMethodUseCaseInputSchema = z.object({
  id: z.string().min(1, "Payment method ID is required"),
  gateway: z.nativeEnum(GatewayCodesEnum),
  last4: z.string().length(4, "Last 4 digits must be exactly 4 characters"),
  label: z.string().min(1, "Payment method label is required"),
  reference: z.string().min(1, "Payment method reference is required"),
  isDefault: z.boolean(),
  userId: z.string().min(1, "User ID is required"),
  type: z.nativeEnum(PaymentMethodTypeEnum),
  expMonth: z.string().regex(/^(0[1-9]|1[0-2])$/, "Expiration month must be in MM format (01-12)"),
  expYear: z.string().regex(/^\d{4}$/, "Expiration year must be in YYYY format"),
  funding: z.nativeEnum(PaymentMethodFundingEnum),
  tenantId: z.string().min(1, "Tenant ID is required"),
  archived: z.boolean(),
});

export type ICreatePaymentMethodUseCase = IUseCase<
  CreatePaymentMethodUseCaseInput,
  CreatePaymentMethodUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.CREATE_PAYMENT_METHOD,
  scope: Lifecycle.Transient,
})
export class CreatePaymentMethodUseCase
  extends UseCase<CreatePaymentMethodUseCaseInput, CreatePaymentMethodUseCaseOutput, UseCaseContext>
  implements ICreatePaymentMethodUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.PAYMENT_METHOD)
    protected readonly builder: IPaymentMethodBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.PAYMENT_METHOD)
    protected readonly repo: IPaymentMethodRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreatePaymentMethodUseCaseInput) => {
    const validatedInput = CreatePaymentMethodUseCaseInputSchema.parse(input);

    // Check if payment method with the same ID already exists
    const existingPaymentMethod = await this.repo.findOne({
      filter: { id: { $eq: validatedInput.id } },
    });
    if (existingPaymentMethod) throw new PaymentMethodAlreadyExistsError();

    // Additional business validation
    this.validateExpirationDate(validatedInput.expMonth, validatedInput.expYear);

    // If this is set as default, ensure no other payment method for this user is default
    if (validatedInput.isDefault) {
      await this.ensureOnlyOneDefaultPerUser(validatedInput.userId, validatedInput.tenantId);
    }

    // Create new payment method
    const paymentMethod = await this.builder.build({ id: validatedInput.id });
    await paymentMethod.create(validatedInput);
    await this.repo.create(paymentMethod);

    return { id: paymentMethod.id };
  };

  private validateExpirationDate(expMonth: string, expYear: string): void {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11

    const expirationYear = parseInt(expYear, 10);
    const expirationMonth = parseInt(expMonth, 10);

    if (
      expirationYear < currentYear ||
      (expirationYear === currentYear && expirationMonth < currentMonth)
    ) {
      throw new PaymentMethodInvalidExpirationError();
    }
  }

  private async ensureOnlyOneDefaultPerUser(userId: string, tenantId: string): Promise<void> {
    const existingDefaultMethods = await this.repo.find({
      filter: {
        userId: { $eq: userId },
        tenantId: { $eq: tenantId },
        isDefault: { $eq: true },
        archived: { $eq: false },
      },
    });

    // If there are existing default methods, we should unset them
    // This would typically be handled by a separate business rule or domain service
    // For now, we'll throw an error to prevent multiple defaults
    if (existingDefaultMethods.length > 0) {
      throw new PaymentMethodMultipleDefaultsError();
    }
  }
}
